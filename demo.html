<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Green Audio Player Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .demo-section {
            background: white;
            padding: 30px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1, h2 {
            color: #333;
        }
        
        .player-container {
            margin: 20px 0;
            display: flex;
            justify-content: center;
        }
        
        .description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        /* Green Audio Player Styles */
        .audio.green-audio-player {
            width: 400px;
            min-width: 300px;
            height: 56px;
            box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.07);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-left: 24px;
            padding-right: 24px;
            border-radius: 4px;
            user-select: none;
            -webkit-user-select: none;
            background-color: #fff;
            margin: 0 auto;
        }

        .audio.green-audio-player .play-pause-btn {
            cursor: pointer;
        }

        .audio.green-audio-player .slider {
            flex-grow: 1;
            background-color: #d8d8d8;
            cursor: pointer;
            position: relative;
        }

        .audio.green-audio-player .slider .progress {
            background-color: #44bfa3;
            border-radius: inherit;
            position: absolute;
            pointer-events: none;
        }

        .audio.green-audio-player .slider .progress .pin {
            height: 16px;
            width: 16px;
            border-radius: 8px;
            background-color: #44bfa3;
            position: absolute;
            pointer-events: all;
            box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.32);
        }

        .audio.green-audio-player .controls {
            font-family: "Roboto", sans-serif;
            font-size: 16px;
            line-height: 18px;
            color: #55606e;
            display: flex;
            flex-grow: 1;
            justify-content: space-between;
            align-items: center;
            margin-left: 24px;
            margin-right: 24px;
        }

        .audio.green-audio-player .controls .slider {
            margin-left: 16px;
            margin-right: 16px;
            border-radius: 2px;
            height: 4px;
        }

        .audio.green-audio-player .controls .slider .progress {
            width: 0;
            height: 100%;
        }

        .audio.green-audio-player .controls .slider .pin {
            right: -8px;
            top: -6px;
        }

        .audio.green-audio-player .controls span {
            cursor: default;
        }

        .audio.green-audio-player .volume {
            position: relative;
        }

        .audio.green-audio-player .volume .volume-btn {
            cursor: pointer;
        }

        .audio.green-audio-player .volume .volume-btn.open path {
            fill: #44bfa3;
        }

        .audio.green-audio-player .volume .volume-controls {
            width: 30px;
            height: 135px;
            background-color: rgba(0, 0, 0, 0.62);
            border-radius: 7px;
            position: absolute;
            left: -3px;
            bottom: 52px;
            flex-direction: column;
            align-items: center;
            display: flex;
        }

        .audio.green-audio-player .volume .volume-controls.hidden {
            display: none;
        }

        .audio.green-audio-player .volume .volume-controls .slider {
            margin-top: 12px;
            margin-bottom: 12px;
            width: 6px;
            border-radius: 3px;
        }

        .audio.green-audio-player .volume .volume-controls .slider .progress {
            bottom: 0;
            height: 100%;
            width: 6px;
        }

        .audio.green-audio-player .volume .volume-controls .slider .progress .pin {
            left: -5px;
            top: -8px;
        }
    </style>
</head>
<body>
    <h1>Green Audio Player Demo</h1>
    
    <div class="demo-section">
        <h2>Green Audio Player Component</h2>
        <p class="description">
            This is the green audio player component that has been implemented in your AureaVoice application. 
            It features a clean, modern design with custom controls for play/pause, progress tracking, and volume control.
        </p>
        
        <div class="player-container">
            <div class="audio green-audio-player">
                <div class="play-pause-btn">
                    <svg viewBox="0 0 18 24" height="24" width="18" xmlns="http://www.w3.org/2000/svg">
                        <path id="playPause" class="play-pause-icon" d="M18 12L0 24V0" fill-rule="evenodd" fill="#566574"></path>
                    </svg>
                </div>

                <div class="controls">
                    <span class="current-time">0:00</span>
                    <div data-direction="horizontal" class="slider">
                        <div class="progress">
                            <div data-method="rewind" id="progress-pin" class="pin"></div>
                        </div>
                    </div>
                    <span class="total-time">3:45</span>
                </div>

                <div class="volume">
                    <div class="volume-btn">
                        <svg viewBox="0 0 24 24" height="24" width="24" xmlns="http://www.w3.org/2000/svg">
                            <path id="speaker" d="M14.667 0v2.747c3.853 1.146 6.666 4.72 6.666 8.946 0 4.227-2.813 7.787-6.666 8.934v2.76C20 22.173 24 17.4 24 11.693 24 5.987 20 1.213 14.667 0zM18 11.693c0-2.36-1.333-4.386-3.333-5.373v10.707c2-.947 3.333-2.987 3.333-5.334zm-18-4v8h5.333L12 22.36V1.027L5.333 7.693H0z" fill-rule="evenodd" fill="#566574"></path>
                        </svg>
                    </div>
                    <div class="volume-controls hidden">
                        <div data-direction="vertical" class="slider">
                            <div class="progress">
                                <div data-method="changeVolume" id="volume-pin" class="pin"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <audio crossorigin=""></audio>
            </div>
        </div>
        
        <div class="description">
            <strong>Features:</strong>
            <ul>
                <li>Clean, modern design with subtle shadows</li>
                <li>Custom SVG icons for play/pause and volume</li>
                <li>Interactive progress bar with draggable pin</li>
                <li>Vertical volume control with hover/click functionality</li>
                <li>Time display showing current and total duration</li>
                <li>Responsive design that works on different screen sizes</li>
            </ul>
        </div>
    </div>

    <div class="demo-section">
        <h2>Integration in AureaVoice</h2>
        <p class="description">
            The green audio player has been successfully integrated into your AureaVoice application:
        </p>
        <ul>
            <li><strong>PlaybackView.js</strong> - Updated to support both default and green player designs</li>
            <li><strong>PlaybackPresenter.js</strong> - Modified to accept green player option</li>
            <li><strong>PlaybackController.js</strong> - Enhanced to create instances with green player</li>
            <li><strong>ResultView.js</strong> - Now uses the green player for audio playback</li>
            <li><strong>components.css</strong> - Added complete styling for the green audio player</li>
        </ul>
        
        <p class="description">
            To test the green audio player in your application:
            <ol>
                <li>Navigate to the welcome page</li>
                <li>Record an audio sample</li>
                <li>View the results page to see the green audio player in action</li>
            </ol>
        </p>
    </div>

    <script>
        // Simple demo functionality for the static player
        document.addEventListener('DOMContentLoaded', function() {
            const playBtn = document.querySelector('.play-pause-btn');
            const volumeBtn = document.querySelector('.volume-btn');
            const volumeControls = document.querySelector('.volume-controls');
            const playPauseIcon = document.querySelector('#playPause');
            
            let isPlaying = false;
            let volumeVisible = false;
            
            // Play/Pause functionality
            playBtn.addEventListener('click', function() {
                isPlaying = !isPlaying;
                if (isPlaying) {
                    // Pause icon (two rectangles)
                    playPauseIcon.setAttribute('d', 'M0 0h6v24H0zM12 0h6v24h-6z');
                } else {
                    // Play icon (triangle)
                    playPauseIcon.setAttribute('d', 'M18 12L0 24V0');
                }
            });
            
            // Volume control toggle
            volumeBtn.addEventListener('click', function() {
                volumeVisible = !volumeVisible;
                if (volumeVisible) {
                    volumeControls.classList.remove('hidden');
                    volumeBtn.classList.add('open');
                } else {
                    volumeControls.classList.add('hidden');
                    volumeBtn.classList.remove('open');
                }
            });
        });
    </script>
</body>
</html>
