/* Layout Styles - Containers and positioning */

/* Container styles */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  position: relative;
}

/* Welcome page styles */
.welcome-text {
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  color: #2d3748;
  margin-bottom: 3rem;
  max-width: 800px;
  line-height: 1.3;
  opacity: 0;
  transform: translateY(20px);
}

.welcome-text.visible {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.5s ease-out;
}

/* Test page styles */
.test-text {
  font-size: 1.8rem;
  font-weight: 500;
  text-align: center;
  color: #2d3748;
  max-width: 1200px;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 1;
}

/* Back button positioning */
.back-button {
  position: absolute;
  top: 2rem;
  left: 2rem;
  z-index: 1000;
}

/* Demo link positioning */
.demo-link {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  color: #4299e1;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border: 1px solid #4299e1;
  border-radius: 0.25rem;
  transition: all 0.3s ease;
}

.demo-link:hover {
  background-color: #4299e1;
  color: white;
}
